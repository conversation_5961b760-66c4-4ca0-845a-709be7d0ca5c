{"name": "vercel-nodejs-app", "version": "1.0.0", "description": "A Node.js application for Vercel deployment", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'Build completed'", "vercel-build": "echo 'Vercel build completed'", "deploy": "node deploy.js", "test": "echo 'No tests specified'"}, "keywords": ["nodejs", "vercel", "deployment"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}