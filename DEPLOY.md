# 🚀 Hướng dẫn Deploy lên Vercel

## Cách 1: Deploy tự động bằng script

```bash
# Chạy script deploy tự động
npm run deploy
```

Script sẽ tự động:
- Kiểm tra và cài đặt Vercel CLI
- Cài đặt dependencies
- Chạy build (nếu có)
- Deploy lên Vercel
- Hiển thị URL deployment

## Cách 2: Deploy thủ công

### Bước 1: Cài đặt Vercel CLI
```bash
npm install -g vercel
```

### Bước 2: Login vào Vercel
```bash
vercel login
```

### Bước 3: Deploy
```bash
# Deploy lần đầu (preview)
vercel

# Deploy production
vercel --prod
```

## Cách 3: Deploy qua GitHub

1. Push code lên GitHub repository
2. Truy cập [vercel.com](https://vercel.com)
3. Click "New Project"
4. Import từ GitHub repository
5. Vercel sẽ tự động deploy

## ⚙️ Cấu hình Environment Variables

### Trên Vercel Dashboard:
1. Vào Project Settings
2. Tab "Environment Variables"
3. Thêm các biến:
   - `NODE_ENV=production`
   - Các biến khác từ .env.example

### Qua CLI:
```bash
vercel env add NODE_ENV production
```

## 🔧 Troubleshooting

### Lỗi thường gặp:

1. **"Command not found: vercel"**
   ```bash
   npm install -g vercel
   ```

2. **"Not logged in"**
   ```bash
   vercel login
   ```

3. **"Build failed"**
   - Kiểm tra package.json
   - Kiểm tra vercel.json
   - Xem logs trên Vercel dashboard

4. **"Function timeout"**
   - Tăng maxDuration trong vercel.json
   - Tối ưu code để chạy nhanh hơn

## 📊 Monitoring

### Xem logs:
```bash
vercel logs [deployment-url]
```

### Xem deployments:
```bash
vercel ls
```

### Xem domains:
```bash
vercel domains ls
```

## 🌐 Custom Domain

### Thêm domain:
```bash
vercel domains add yourdomain.com
```

### Hoặc qua Dashboard:
1. Project Settings
2. Domains tab
3. Add domain

## 📈 Performance Tips

1. **Optimize dependencies**
   - Chỉ install dependencies cần thiết
   - Sử dụng devDependencies cho dev tools

2. **Minimize function size**
   - Tránh import không cần thiết
   - Sử dụng dynamic imports khi có thể

3. **Cache static assets**
   - Đặt static files trong thư mục public
   - Sử dụng CDN cho assets lớn

## 🔒 Security

1. **Environment Variables**
   - Không commit .env files
   - Sử dụng Vercel env cho production

2. **API Security**
   - Implement rate limiting
   - Validate input data
   - Use HTTPS only

## 📞 Support

- [Vercel Documentation](https://vercel.com/docs)
- [Vercel Community](https://github.com/vercel/vercel/discussions)
- [Vercel Discord](https://vercel.com/discord)
