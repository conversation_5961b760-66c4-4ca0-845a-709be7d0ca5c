<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vercel Node.js App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 3rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .api-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        
        .api-endpoint {
            background: rgba(0, 0, 0, 0.2);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 10px;
            text-align: left;
        }
        
        .method {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            border-radius: 5px;
            font-weight: bold;
            margin-right: 1rem;
            font-size: 0.9rem;
        }
        
        .get { background: #28a745; }
        .post { background: #007bff; }
        
        .endpoint-url {
            font-family: 'Courier New', monospace;
            color: #ffd700;
        }
        
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            margin: 0.5rem;
            transition: transform 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(40, 167, 69, 0.2);
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Vercel Node.js App</h1>
        <p>Ứng dụng Node.js được deploy thành công lên Vercel!</p>
        
        <div class="api-section">
            <h3>📡 API Endpoints</h3>
            
            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-url">/api/health</span>
                <p>Health check endpoint</p>
            </div>
            
            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-url">/api/users</span>
                <p>Lấy danh sách users</p>
            </div>
            
            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="endpoint-url">/api/users</span>
                <p>Tạo user mới</p>
            </div>
            
            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-url">/api/time</span>
                <p>Lấy thời gian hiện tại</p>
            </div>
        </div>
        
        <div>
            <a href="/api/health" class="btn">Test Health API</a>
            <a href="/api/users" class="btn">Test Users API</a>
            <a href="/api/time" class="btn">Test Time API</a>
        </div>
        
        <div class="status">
            <strong>✅ Status:</strong> Application is running successfully!
        </div>
    </div>
</body>
</html>
