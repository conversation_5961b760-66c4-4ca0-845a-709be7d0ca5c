// Game constants
const PADDLE_WIDTH = 100;
const PADDLE_HEIGHT = 15;
const PADDLE_SPEED = 8;
const BALL_RADIUS = 8;
const BRICK_ROWS = 5;
const BRICK_COLUMNS = 10;
const BRICK_WIDTH = 75;
const BRICK_HEIGHT = 20;
const BRICK_PADDING = 10;
const BRICK_OFFSET_TOP = 80;
const BRICK_OFFSET_LEFT = 35;
const PARTICLE_COUNT = 15;
const TRAIL_COUNT = 10;

// Game variables
let canvas, ctx;
let paddle, ball;
let bricks = [];
let particles = [];
let trails = [];
let score = 0;
let lives = 3;
let level = 1;
let gameState = 'start'; // start, playing, levelComplete, gameOver
let rightPressed = false;
let leftPressed = false;
let lastTime = 0;
let animationId;

// Neon colors
const neonColors = [
    '#ff00ff', // pink
    '#00ffff', // cyan
    '#00ff00', // green
    '#ffff00', // yellow
    '#9d00ff'  // purple
];

// DOM elements
const scoreElement = document.getElementById('score');
const livesElement = document.getElementById('lives');
const startScreen = document.getElementById('startScreen');
const gameOverScreen = document.getElementById('gameOverScreen');
const levelCompleteScreen = document.getElementById('levelCompleteScreen');
const finalScoreElement = document.getElementById('finalScore');
const levelScoreElement = document.getElementById('levelScore');
const startButton = document.getElementById('startButton');
const restartButton = document.getElementById('restartButton');
const nextLevelButton = document.getElementById('nextLevelButton');

// Initialize the game
function init() {
    canvas = document.getElementById('gameCanvas');
    ctx = canvas.getContext('2d');
    
    // Set canvas dimensions
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
    
    // Create paddle
    paddle = {
        x: canvas.width / 2 - PADDLE_WIDTH / 2,
        y: canvas.height - PADDLE_HEIGHT - 10,
        width: PADDLE_WIDTH,
        height: PADDLE_HEIGHT,
        dx: 0,
        color: neonColors[1]
    };
    
    // Create ball
    resetBall();
    
    // Create bricks
    createBricks();
    
    // Event listeners
    document.addEventListener('keydown', keyDownHandler);
    document.addEventListener('keyup', keyUpHandler);
    document.addEventListener('mousemove', mouseMoveHandler);
    
    startButton.addEventListener('click', startGame);
    restartButton.addEventListener('click', restartGame);
    nextLevelButton.addEventListener('click', nextLevel);
    
    // Show start screen
    startScreen.classList.remove('hidden');
    gameOverScreen.classList.add('hidden');
    levelCompleteScreen.classList.add('hidden');
    
    // Initial render
    render();
}

// Create bricks
function createBricks() {
    bricks = [];
    
    for (let r = 0; r < BRICK_ROWS; r++) {
        for (let c = 0; c < BRICK_COLUMNS; c++) {
            const brickX = c * (BRICK_WIDTH + BRICK_PADDING) + BRICK_OFFSET_LEFT;
            const brickY = r * (BRICK_HEIGHT + BRICK_PADDING) + BRICK_OFFSET_TOP;
            
            // Assign different colors to different rows
            const colorIndex = r % neonColors.length;
            
            bricks.push({
                x: brickX,
                y: brickY,
                width: BRICK_WIDTH,
                height: BRICK_HEIGHT,
                status: 1, // 1 = active, 0 = broken
                color: neonColors[colorIndex],
                points: (BRICK_ROWS - r) * 10 // Higher rows worth more points
            });
        }
    }
}

// Reset ball position
function resetBall() {
    ball = {
        x: canvas.width / 2,
        y: canvas.height - PADDLE_HEIGHT - 30,
        radius: BALL_RADIUS,
        dx: 4 * (Math.random() > 0.5 ? 1 : -1), // Random direction
        dy: -4 - level * 0.5, // Increase speed with level
        color: neonColors[0]
    };
    
    // Clear trails when resetting ball
    trails = [];
}

// Start the game
function startGame() {
    gameState = 'playing';
    startScreen.classList.add('hidden');
    gameLoop(0);
}

// Restart the game
function restartGame() {
    score = 0;
    lives = 3;
    level = 1;
    updateScore();
    updateLives();
    resetBall();
    createBricks();
    gameState = 'playing';
    gameOverScreen.classList.add('hidden');
    gameLoop(0);
}

// Next level
function nextLevel() {
    level++;
    resetBall();
    createBricks();
    gameState = 'playing';
    levelCompleteScreen.classList.add('hidden');
    gameLoop(0);
}

// Update score
function updateScore() {
    scoreElement.textContent = score;
}

// Update lives
function updateLives() {
    livesElement.textContent = lives;
}

// Key event handlers
function keyDownHandler(e) {
    if (e.key === 'Right' || e.key === 'ArrowRight') {
        rightPressed = true;
    } else if (e.key === 'Left' || e.key === 'ArrowLeft') {
        leftPressed = true;
    }
}

function keyUpHandler(e) {
    if (e.key === 'Right' || e.key === 'ArrowRight') {
        rightPressed = false;
    } else if (e.key === 'Left' || e.key === 'ArrowLeft') {
        leftPressed = false;
    }
}

function mouseMoveHandler(e) {
    if (gameState === 'playing') {
        const relativeX = e.clientX - canvas.getBoundingClientRect().left;
        if (relativeX > 0 && relativeX < canvas.width) {
            paddle.x = relativeX - paddle.width / 2;
            
            // Keep paddle within canvas
            if (paddle.x < 0) {
                paddle.x = 0;
            } else if (paddle.x + paddle.width > canvas.width) {
                paddle.x = canvas.width - paddle.width;
            }
        }
    }
}

// Create particles for brick hit effect
function createParticles(x, y, color) {
    for (let i = 0; i < PARTICLE_COUNT; i++) {
        const angle = Math.random() * Math.PI * 2;
        const speed = Math.random() * 3 + 2;
        
        particles.push({
            x: x,
            y: y,
            radius: Math.random() * 3 + 1,
            color: color,
            dx: Math.cos(angle) * speed,
            dy: Math.sin(angle) * speed,
            alpha: 1,
            life: Math.random() * 30 + 30
        });
    }
}

// Update particles
function updateParticles() {
    for (let i = particles.length - 1; i >= 0; i--) {
        const p = particles[i];
        
        p.x += p.dx;
        p.y += p.dy;
        p.life--;
        p.alpha = p.life / 60;
        
        if (p.life <= 0) {
            particles.splice(i, 1);
        }
    }
}

// Create ball trail
function createTrail() {
    trails.push({
        x: ball.x,
        y: ball.y,
        radius: ball.radius,
        color: ball.color,
        alpha: 1
    });
    
    if (trails.length > TRAIL_COUNT) {
        trails.shift();
    }
}

// Update ball trail
function updateTrails() {
    for (let i = 0; i < trails.length; i++) {
        trails[i].alpha -= 0.1;
    }
}

// Collision detection
function collisionDetection() {
    // Check brick collisions
    for (let i = 0; i < bricks.length; i++) {
        const brick = bricks[i];
        
        if (brick.status === 1) {
            if (ball.x + ball.radius > brick.x && 
                ball.x - ball.radius < brick.x + brick.width && 
                ball.y + ball.radius > brick.y && 
                ball.y - ball.radius < brick.y + brick.height) {
                
                ball.dy = -ball.dy;
                brick.status = 0;
                score += brick.points;
                updateScore();
                
                // Create particles at brick position
                createParticles(brick.x + brick.width/2, brick.y + brick.height/2, brick.color);
                
                // Check if level is complete
                if (bricks.every(b => b.status === 0)) {
                    levelComplete();
                }
            }
        }
    }
    
    // Check paddle collision
    if (ball.x + ball.radius > paddle.x && 
        ball.x - ball.radius < paddle.x + paddle.width && 
        ball.y + ball.radius > paddle.y) {
        
        // Calculate bounce angle based on where ball hits paddle
        const hitPoint = (ball.x - (paddle.x + paddle.width / 2)) / (paddle.width / 2);
        const angle = hitPoint * (Math.PI / 3); // Max 60 degree angle
        
        ball.dx = Math.sin(angle) * (4 + level * 0.5);
        ball.dy = -Math.cos(angle) * (4 + level * 0.5);
        
        // Create particles for paddle hit
        createParticles(ball.x, ball.y, paddle.color);
    }
    
    // Check wall collisions
    if (ball.x + ball.radius > canvas.width || ball.x - ball.radius < 0) {
        ball.dx = -ball.dx;
        createParticles(ball.x, ball.y, ball.color);
    }
    
    if (ball.y - ball.radius < 0) {
        ball.dy = -ball.dy;
        createParticles(ball.x, ball.y, ball.color);
    }
    
    // Check bottom collision (lose life)
    if (ball.y + ball.radius > canvas.height) {
        lives--;
        updateLives();
        
        if (lives <= 0) {
            gameOver();
        } else {
            resetBall();
        }
    }
}

// Level complete
function levelComplete() {
    gameState = 'levelComplete';
    levelScoreElement.textContent = score;
    levelCompleteScreen.classList.remove('hidden');
    cancelAnimationFrame(animationId);
}

// Game over
function gameOver() {
    gameState = 'gameOver';
    finalScoreElement.textContent = score;
    gameOverScreen.classList.remove('hidden');
    cancelAnimationFrame(animationId);
}

// Update game state
function update(deltaTime) {
    // Move paddle
    if (rightPressed && paddle.x + paddle.width < canvas.width) {
        paddle.x += PADDLE_SPEED;
    } else if (leftPressed && paddle.x > 0) {
        paddle.x -= PADDLE_SPEED;
    }
    
    // Move ball
    ball.x += ball.dx;
    ball.y += ball.dy;
    
    // Create and update ball trail
    createTrail();
    updateTrails();
    
    // Update particles
    updateParticles();
    
    // Check collisions
    collisionDetection();
}

// Render game objects
function render() {
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background grid
    drawGrid();
    
    // Draw ball trails
    for (let i = 0; i < trails.length; i++) {
        const trail = trails[i];
        ctx.beginPath();
        ctx.arc(trail.x, trail.y, trail.radius, 0, Math.PI * 2);
        ctx.fillStyle = trail.color + Math.floor(trail.alpha * 255).toString(16).padStart(2, '0');
        ctx.fill();
        ctx.closePath();
    }
    
    // Draw ball
    ctx.beginPath();
    ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
    ctx.fillStyle = ball.color;
    ctx.fill();
    ctx.shadowBlur = 15;
    ctx.shadowColor = ball.color;
    ctx.fill();
    ctx.shadowBlur = 0;
    ctx.closePath();
    
    // Draw paddle
    ctx.beginPath();
    ctx.rect(paddle.x, paddle.y, paddle.width, paddle.height);
    ctx.fillStyle = paddle.color;
    ctx.fill();
    ctx.shadowBlur = 15;
    ctx.shadowColor = paddle.color;
    ctx.fill();
    ctx.shadowBlur = 0;
    ctx.closePath();
    
    // Draw bricks
    for (let i = 0; i < bricks.length; i++) {
        const brick = bricks[i];
        
        if (brick.status === 1) {
            ctx.beginPath();
            ctx.rect(brick.x, brick.y, brick.width, brick.height);
            ctx.fillStyle = brick.color;
            ctx.fill();
            ctx.shadowBlur = 10;
            ctx.shadowColor = brick.color;
            ctx.fill();
            ctx.shadowBlur = 0;
            ctx.closePath();
        }
    }
    
    // Draw particles
    for (let i = 0; i < particles.length; i++) {
        const p = particles[i];
        
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
        ctx.fillStyle = p.color + Math.floor(p.alpha * 255).toString(16).padStart(2, '0');
        ctx.fill();
        ctx.closePath();
    }
}

// Draw background grid
function drawGrid() {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
    ctx.lineWidth = 1;
    
    // Vertical lines
    for (let x = 0; x < canvas.width; x += 40) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
    }
    
    // Horizontal lines
    for (let y = 0; y < canvas.height; y += 40) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
    }
}

// Game loop
function gameLoop(timestamp) {
    const deltaTime = timestamp - lastTime;
    lastTime = timestamp;
    
    if (gameState === 'playing') {
        update(deltaTime);
        render();
        animationId = requestAnimationFrame(gameLoop);
    }
}

// Initialize the game when the window loads
window.addEventListener('load', init);
