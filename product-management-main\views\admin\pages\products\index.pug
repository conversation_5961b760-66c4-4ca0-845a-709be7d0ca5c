extends ../../layouts/default.pug
include ../../mixins/filter-status.pug
include ../../mixins/search.pug
include ../../mixins/pagination.pug
include ../../mixins/form-change-multi.pug
include ../../mixins/alert.pug



block main 

    +alert-success("5000")
    +alert-error("5000")

    h1 Danh <PERSON>ch <PERSON>ản <PERSON>m

    .card.mb-3 
        .card-header Bộ <PERSON>c <PERSON> Tì<PERSON>ếm
        .card-body 
            .row 
                .col-6
                    +filter-status(filterStatus)
                .col-6 
                    +search(keyword)
                            
    .card.mb-3 
        .cart.header Bộ lọc Tìm Kiếm 
        .cart.body 
            .row    
                .col-4 

    .card.mb-3 
        .card-header <PERSON><PERSON> 
        .card-body 
            .row
                .col-8 
                    +formChangeMulti(`${prefixAdmin}/products/change-multi?_method=PATCH`)
                .col-4
                    a(
                        href=`${prefixAdmin}/products/create`
                        class="btn btn-outline-success" 
                    ) creat +
            table(
                class = "table table-hover table-sm "
                checkbox-multi
            )
                thead 
                    tr 
                        th 
                            input(type="checkbox" name="checkAll")
                        th STT
                        th H<PERSON>nh ảnh 
                        th Tiêu đề
                        th Giá
                        th Vị Trí
                        th Trạng Thái 
                        th View
                        th Hành động
                tbody 
                    each item, index in products 
                        tr  
                            td 
                                input(
                                    type="checkbox"
                                    name="checkId"
                                    value=item.id
                                )
                            td #{index+1}
                            td 
                                img(
                                    src=item.thumbnail, 
                                    alt=item.title,
                                    width="100px",
                                    height="auto"
                                )
                                td #{item.title}
                                td #{item.price}$

                                td  
                                    input(
                                        type="number"
                                        value=item.position 
                                        style="width: 60px"
                                        min="1"
                                        name="position"
                                    )

                                td 
                                    if(item.status == "active")
                                            a(
                                                href="javascript:;" 
                                                data-status=item.status
                                                data-id= item.id
                                                button-change-status
                                                class="badge badge-success"
                                            ) Hoạt động
                                    else
                                            a(
                                                href="javascript:;" 
                                                data-status=item.status
                                                data-id= item.id
                                                button-change-status
                                                class="badge badge-danger"
                                            ) DỪng hoạt động
                                td 
                                    a(
                                        href=`${prefixAdmin}/products/detail/${item.id}`,
                                        class="btn btn-secondary btn-sm"
                                    ) chi tiết

                                td  
                                    a(
                                        href=`${prefixAdmin}/products/edit/${item.id}`
                                        class="btn btn-primary btn-sm"
                                    ) Sửa
                                    button(
                                        class="btn btn-danger btn-sm ml-1"
                                        button-delete-id 
                                          data-id= item.id
                                    ) Xóa  
    +pagination(pagination)

    .card-footer Contact me !!!!

    form(
        action=""
        method="POST"
        id="form-change-status"
        data-path=`${prefixAdmin}/products/change-status`
    )

    form(
        action=""
        method="POST"
        id="form-change-id"
        data-path=`${prefixAdmin}/products/delete`
    )