@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap');

:root {
    --neon-pink: #ff00ff;
    --neon-blue: #00ffff;
    --neon-green: #00ff00;
    --neon-yellow: #ffff00;
    --neon-purple: #9d00ff;
    --background: #0a0a0a;
    --text-glow: 0 0 10px rgba(255, 255, 255, 0.8);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--background);
    font-family: 'Orbitron', sans-serif;
    color: white;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-image: 
        radial-gradient(circle at 10% 20%, rgba(255, 0, 255, 0.05) 0%, transparent 20%),
        radial-gradient(circle at 90% 80%, rgba(0, 255, 255, 0.05) 0%, transparent 20%),
        radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 30%);
}

.game-container {
    position: relative;
    width: 800px;
    height: 600px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    box-shadow: 
        0 0 20px rgba(255, 0, 255, 0.3),
        0 0 40px rgba(0, 255, 255, 0.2),
        inset 0 0 15px rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.game-header {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
    background-color: rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 10;
}

.score-container, .lives-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.score-label, .lives-label {
    font-size: 14px;
    opacity: 0.7;
    letter-spacing: 2px;
}

#score, #lives {
    font-size: 24px;
    font-weight: bold;
    text-shadow: 
        0 0 5px var(--neon-blue),
        0 0 10px var(--neon-blue);
}

#gameCanvas {
    background-color: transparent;
    display: block;
    width: 100%;
    height: calc(100% - 60px);
}

.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.85);
    z-index: 100;
}

.hidden {
    display: none;
}

.game-title {
    font-size: 60px;
    margin-bottom: 40px;
    letter-spacing: 5px;
    background: linear-gradient(to right, var(--neon-pink), var(--neon-blue));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 
        0 0 10px rgba(255, 0, 255, 0.7),
        0 0 20px rgba(0, 255, 255, 0.5);
    animation: pulse 2s infinite;
}

.game-over-title, .level-complete-title {
    font-size: 48px;
    margin-bottom: 30px;
    letter-spacing: 3px;
}

.game-over-title {
    color: var(--neon-pink);
    text-shadow: 
        0 0 10px var(--neon-pink),
        0 0 20px var(--neon-pink);
}

.level-complete-title {
    color: var(--neon-green);
    text-shadow: 
        0 0 10px var(--neon-green),
        0 0 20px var(--neon-green);
}

.final-score, .level-score {
    font-size: 24px;
    margin-bottom: 30px;
    text-shadow: var(--text-glow);
}

.neon-button {
    background: transparent;
    border: 2px solid var(--neon-blue);
    color: white;
    padding: 12px 30px;
    font-family: 'Orbitron', sans-serif;
    font-size: 18px;
    letter-spacing: 2px;
    cursor: pointer;
    border-radius: 5px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px var(--neon-blue);
    text-shadow: 0 0 5px white;
    position: relative;
    overflow: hidden;
}

.neon-button:hover {
    background-color: rgba(0, 255, 255, 0.1);
    box-shadow: 
        0 0 15px var(--neon-blue),
        0 0 30px var(--neon-blue);
    transform: translateY(-2px);
}

.neon-button:active {
    transform: translateY(1px);
}

@keyframes pulse {
    0% {
        text-shadow: 
            0 0 10px rgba(255, 0, 255, 0.7),
            0 0 20px rgba(0, 255, 255, 0.5);
    }
    50% {
        text-shadow: 
            0 0 15px rgba(255, 0, 255, 0.9),
            0 0 30px rgba(0, 255, 255, 0.7),
            0 0 40px rgba(0, 255, 255, 0.5);
    }
    100% {
        text-shadow: 
            0 0 10px rgba(255, 0, 255, 0.7),
            0 0 20px rgba(0, 255, 255, 0.5);
    }
}
