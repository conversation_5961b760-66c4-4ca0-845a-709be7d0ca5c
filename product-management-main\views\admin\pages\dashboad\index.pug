//- extends: <PERSON><PERSON> 
extends ../../layouts/default.pug

block main 
    h1 Trang Tổng quan
//
Editorial by HTML5 UP
html5up.net | @ajlkn
Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
head
title Editorial by HTML5 UP
meta(charset='utf-8')
meta(name='viewport' content='width=device-width, initial-scale=1, user-scalable=no')
link(rel='stylesheet' href='assets/css/main.css')
// Wrapper
#wrapper
// Main
#main
.inner
// Header
header#header
a.logo(href='index.html')
    strong Editorial
    |  by HTML5 UP
ul.icons
    li
    a.icon.brands.fa-twitter(href='#')
        span.label Twitter
    li
    a.icon.brands.fa-facebook-f(href='#')
        span.label Facebook
    li
    a.icon.brands.fa-snapchat-ghost(href='#')
        span.label Snapchat
    li
    a.icon.brands.fa-instagram(href='#')
        span.label Instagram
    li
    a.icon.brands.fa-medium-m(href='#')
        span.label Medium
// Banner
section#banner
.content
    header
    h1
        | Hi, I&rsquo;m Editorial
        br
        | &#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;by HTML5 UP
    p A free and fully responsive site template
    p
    | Aenean ornare velit lacus, ac varius enim ullamcorper eu. Proin aliquam facilisis ante interdum congue. Integer mollis, nisl amet convallis, porttitor magna ullamcorper, amet egestas mauris. Ut magna finibus nisi nec lacinia. Nam maximus erat id euismod egestas. Pellentesque sapien ac quam. Lorem ipsum dolor sit nullam.
    ul.actions
    li
        a.button.big(href='#') Learn More
span.image.object
    img(src='images/pic10.jpg' alt='')
// Section
section
header.major
    h2 Erat lacinia
.features
    article
    span.icon.fa-gem
    .content
        h3 Portitor ullamcorper
        p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    article
    span.icon.solid.fa-paper-plane
    .content
        h3 Sapien veroeros
        p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    article
    span.icon.solid.fa-rocket
    .content
        h3 Quam lorem ipsum
        p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    article
    span.icon.solid.fa-signal
    .content
        h3 Sed magna finibus
        p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
// Section
section
header.major
    h2 Ipsum sed dolor
.posts
    article
    a.image(href='#')
        img(src='images/pic01.jpg' alt='')
    h3 Interdum aenean
    p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    ul.actions
        li
        a.button(href='#') More
    article
    a.image(href='#')
        img(src='images/pic02.jpg' alt='')
    h3 Nulla amet dolore
    p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    ul.actions
        li
        a.button(href='#') More
    article
    a.image(href='#')
        img(src='images/pic03.jpg' alt='')
    h3 Tempus ullamcorper
    p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    ul.actions
        li
        a.button(href='#') More
    article
    a.image(href='#')
        img(src='images/pic04.jpg' alt='')
    h3 Sed etiam facilis
    p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    ul.actions
        li
        a.button(href='#') More
    article
    a.image(href='#')
        img(src='images/pic05.jpg' alt='')
    h3 Feugiat lorem aenean
    p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    ul.actions
        li
        a.button(href='#') More
    article
    a.image(href='#')
        img(src='images/pic06.jpg' alt='')
    h3 Amet varius aliquam
    p
        | Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
    ul.actions
        li
        a.button(href='#') More
// Sidebar
#sidebar
.inner
// Search
section#search.alt
form(method='post' action='#')
    input#query(type='text' name='query' placeholder='Search')
// Menu
nav#menu
header.major
    h2 Menu
ul
    li
    a(href='index.html') Homepage
    li
    a(href='generic.html') Generic
    li
    a(href='elements.html') Elements
    li
    span.opener Submenu
    ul
        li
        a(href='#') Lorem Dolor
        li
        a(href='#') Ipsum Adipiscing
        li
        a(href='#') Tempus Magna
        li
        a(href='#') Feugiat Veroeros
    li
    a(href='#') Etiam Dolore
    li
    a(href='#') Adipiscing
    li
    span.opener Another Submenu
    ul
        li
        a(href='#') Lorem Dolor
        li
        a(href='#') Ipsum Adipiscing
        li
        a(href='#') Tempus Magna
        li
        a(href='#') Feugiat Veroeros
    li
    a(href='#') Maximus Erat
    li
    a(href='#') Sapien Mauris
    li
    a(href='#') Amet Lacinia
// Section
section
header.major
    h2 Ante interdum
.mini-posts
    article
    a.image(href='#')
        img(src='images/pic07.jpg' alt='')
    p Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore aliquam.
    article
    a.image(href='#')
        img(src='images/pic08.jpg' alt='')
    p Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore aliquam.
    article
    a.image(href='#')
        img(src='images/pic09.jpg' alt='')
    p Aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore aliquam.
ul.actions
    li
    a.button(href='#') More
// Section
section
header.major
    h2 Get in touch
p
    | Sed varius enim lorem ullamcorper dolore aliquam aenean ornare velit lacus, ac varius enim lorem ullamcorper dolore. Proin sed aliquam facilisis ante interdum. Sed nulla amet lorem feugiat tempus aliquam.
ul.contact
    li.icon.solid.fa-envelope
    a(href='#') <EMAIL>
    li.icon.solid.fa-phone (*************
    li.icon.solid.fa-home
    | 1234 Somewhere Road #8254
    br
    | &#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;&#x9;Nashville, TN 00000-0000
// Footer
footer#footer
p.copyright
    | &copy; Untitled. All rights reserved. Demo Images: 
    a(href='https://unsplash.com') Unsplash
    | . Design: 
    a(href='https://html5up.net') HTML5 UP
    | .
// Scripts
script(src='assets/js/jquery.min.js')
script(src='assets/js/browser.min.js')
script(src='assets/js/breakpoints.min.js')
script(src='assets/js/util.js')
script(src='assets/js/main.js')
