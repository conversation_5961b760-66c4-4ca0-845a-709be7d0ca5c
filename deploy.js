#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Vercel deployment process...\n');

// Check if package.json exists
if (!fs.existsSync('package.json')) {
    console.error('❌ package.json not found!');
    process.exit(1);
}

// Check if vercel.json exists
if (!fs.existsSync('vercel.json')) {
    console.error('❌ vercel.json not found!');
    process.exit(1);
}

try {
    // Check if Vercel CLI is installed
    console.log('📦 Checking Vercel CLI...');
    try {
        execSync('vercel --version', { stdio: 'pipe' });
        console.log('✅ Vercel CLI is installed');
    } catch (error) {
        console.log('📥 Installing Vercel CLI...');
        execSync('npm install -g vercel', { stdio: 'inherit' });
        console.log('✅ Vercel CLI installed successfully');
    }

    // Install dependencies
    console.log('\n📦 Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed successfully');

    // Run build if build script exists
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (packageJson.scripts && packageJson.scripts.build) {
        console.log('\n🔨 Running build script...');
        execSync('npm run build', { stdio: 'inherit' });
        console.log('✅ Build completed successfully');
    }

    // Deploy to Vercel
    console.log('\n🚀 Deploying to Vercel...');
    const deployResult = execSync('vercel --prod', { 
        stdio: 'pipe',
        encoding: 'utf8'
    });

    // Extract deployment URL
    const lines = deployResult.split('\n');
    const deploymentUrl = lines.find(line => line.includes('https://'));
    
    if (deploymentUrl) {
        console.log('✅ Deployment successful!');
        console.log(`🌐 Your app is live at: ${deploymentUrl.trim()}`);
        
        // Save deployment info
        const deploymentInfo = {
            url: deploymentUrl.trim(),
            timestamp: new Date().toISOString(),
            status: 'success'
        };
        
        fs.writeFileSync('deployment-info.json', JSON.stringify(deploymentInfo, null, 2));
        console.log('📄 Deployment info saved to deployment-info.json');
    } else {
        console.log('✅ Deployment completed, but URL not found in output');
        console.log('Check your Vercel dashboard for the deployment URL');
    }

    console.log('\n🎉 Deployment process completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Visit your deployment URL to test the application');
    console.log('2. Check Vercel dashboard for deployment details');
    console.log('3. Configure environment variables if needed');
    console.log('4. Set up custom domain if required');

} catch (error) {
    console.error('\n❌ Deployment failed!');
    console.error('Error:', error.message);
    
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Make sure you are logged in to Vercel: vercel login');
    console.log('2. Check your internet connection');
    console.log('3. Verify your project configuration');
    console.log('4. Check Vercel dashboard for error details');
    
    process.exit(1);
}
