extends ../../layouts/default.pug
include ../../mixins/filter-status.pug
include ../../mixins/search.pug
include ../../mixins/pagination.pug

block main 
    h1 Danh Sách Sản Phẩm

    .card.mb-3 
        .card-header B<PERSON> Và Tìm Kiếm
        .card-body 
            .row 
                .col-6
                    +filter-status(filterStatus)
                    
                .col-6 
                    +search(keyword)
    table.table.table-hover.table-sm 
        thead 
            tr  
                th STT 
                th Hình ảnh 
                th Tiê<PERSON> đề
                th Giá
                th Trạng Th<PERSON>i 
                th Hành động
        tbody 
            each item, index in products 
                tr 
                    td #{index+1}
                    td 
                        img(
                            src=item.thumbnail, 
                            alt=item.title,
                            width="100px",
                            height="auto"
                        )
                    td #{item.title}
                    td #{item.price}$
                    td 
                        if(item.status == "active")
                            a(
                                href="javascript:;" 
                                data-status=item.status
                                data-id= item.id
                                button-change-status
                                class="badge badge-success"
                            ) Hoạt động
                        else
                            a(
                                href="javascript:;" 
                                data-status=item.status
                                data-id= item.id
                                button-change-status
                                class="badge badge-danger"
                                class="badge badge-danger"
                            ) DỪng hoạt động
                    td
                        button(class="btn btn-warning btn-sm") Sửa
                        button(class="btn btn-danger btn-sm ml-1") Xóa 
                        
    +pagination(pagination)


    form(
        action=""
        method="POST"
        id="form-change-status"
        data-path="/admin/product/change-status"
    )
