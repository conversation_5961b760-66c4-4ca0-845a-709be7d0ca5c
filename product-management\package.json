{"name": "product-management", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon --inspect index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"cookie-parser": "^1.4.7", "cookieparser": "^0.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-flash": "^0.0.2", "express-session": "^1.18.1", "method-override": "^3.0.0", "mongodb": "^6.14.2", "mongoose": "^8.12.1", "pug": "^3.0.3"}, "devDependencies": {"nodemon": "^3.1.9"}}