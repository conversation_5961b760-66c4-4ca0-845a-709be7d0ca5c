# Vercel Node.js Deployment App

Một ứng dụng Node.js đơn giản đư<PERSON><PERSON> thiết kế để deploy lên Vercel.

## 🚀 Tính năng

- ✅ RESTful API với Express.js
- ✅ CORS được cấu hình
- ✅ Error handling middleware
- ✅ Health check endpoint
- ✅ User management API (demo)
- ✅ Time API với timezone Việt Nam
- ✅ Tối ưu cho Vercel deployment

## 📋 API Endpoints

### GET /
- Tr<PERSON> về thông tin chào mừng và status của app

### GET /api/health
- Health check endpoint
- Trả về status và uptime

### GET /api/users
- Lấy danh sách users (demo data)

### POST /api/users
- Tạo user mới
- Body: `{ "name": "string", "email": "string" }`

### GET /api/time
- Lấy thời gian hiện tại với timezone Việt Nam

## 🛠️ Cài đặt và Chạy Local

1. Clone repository:
```bash
git clone <your-repo-url>
cd vercel-nodejs-app
```

2. Cài đặt dependencies:
```bash
npm install
```

3. Tạo file .env:
```bash
cp .env.example .env
```

4. Chạy ứng dụng:
```bash
# Development mode
npm run dev

# Production mode
npm start
```

5. Truy cập: http://localhost:3000

## 🚀 Deploy lên Vercel

### Cách 1: Sử dụng Vercel CLI

1. Cài đặt Vercel CLI:
```bash
npm install -g vercel
```

2. Login vào Vercel:
```bash
vercel login
```

3. Deploy:
```bash
vercel
```

4. Theo dõi hướng dẫn và chọn cấu hình phù hợp.

### Cách 2: Sử dụng GitHub Integration

1. Push code lên GitHub repository
2. Truy cập [vercel.com](https://vercel.com)
3. Import project từ GitHub
4. Vercel sẽ tự động detect và deploy

### Cách 3: Sử dụng Vercel Dashboard

1. Truy cập [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import từ Git repository hoặc upload folder
4. Configure và deploy

## ⚙️ Cấu hình Environment Variables trên Vercel

Trong Vercel Dashboard:
1. Vào Project Settings
2. Chọn tab "Environment Variables"
3. Thêm các biến môi trường cần thiết:
   - `NODE_ENV=production`
   - Các biến khác từ file .env

## 📁 Cấu trúc Project

```
vercel-nodejs-app/
├── index.js              # Main application file
├── package.json          # Dependencies và scripts
├── vercel.json           # Vercel configuration
├── .env.example          # Environment variables template
├── README.md             # Documentation
└── public/               # Static files (nếu có)
```

## 🔧 Vercel Configuration

File `vercel.json` chứa cấu hình cho Vercel:
- Build settings
- Route configuration
- Environment variables
- Function settings

## 📝 Notes

- Ứng dụng sử dụng Express.js framework
- Tối ưu cho serverless functions của Vercel
- Hỗ trợ CORS cho frontend integration
- Error handling và 404 routes được cấu hình
- Sẵn sàng cho production deployment

## 🤝 Contributing

1. Fork the project
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📄 License

MIT License
