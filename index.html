<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neon Brick Breaker</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <div class="score-container">
                <span class="score-label">SCORE</span>
                <span id="score">0</span>
            </div>
            <div class="lives-container">
                <span class="lives-label">LIVES</span>
                <span id="lives">3</span>
            </div>
        </div>
        
        <canvas id="gameCanvas"></canvas>
        
        <div id="startScreen" class="screen">
            <h1 class="game-title">NEON BREAKER</h1>
            <button id="startButton" class="neon-button">START GAME</button>
        </div>
        
        <div id="gameOverScreen" class="screen hidden">
            <h2 class="game-over-title">GAME OVER</h2>
            <div class="final-score">SCORE: <span id="finalScore">0</span></div>
            <button id="restartButton" class="neon-button">PLAY AGAIN</button>
        </div>
        
        <div id="levelCompleteScreen" class="screen hidden">
            <h2 class="level-complete-title">LEVEL COMPLETE!</h2>
            <div class="level-score">SCORE: <span id="levelScore">0</span></div>
            <button id="nextLevelButton" class="neon-button">NEXT LEVEL</button>
        </div>
    </div>
    
    <script src="game.js"></script>
</body>
</html>
