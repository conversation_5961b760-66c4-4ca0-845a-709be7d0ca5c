const mongose = require("mongoose");

const productSchema = new mongose.Schema({
  title: String,
  description: String,
  price: Number,
  discountPercentage: Number,
  stock: Number,
  thumbnail: String,
  status: String,
  position: Number,
  deleted: Boolean,
});
// mongoose.model: <PERSON><PERSON><PERSON> là một hàm trong thư viện Mongoose, đ<PERSON><PERSON>c sử dụng để tạo ra một model.
//"Product": Đ<PERSON>y là tên của model, bạn sẽ sử dụng tên này để tương tác với collection "products" trong database.
// productSchema: Đ<PERSON><PERSON> là một schema (sơ đồ) định nghĩa cấu trúc của dữ liệu trong collection "products". N<PERSON> xác định các trường (fields), kiểu dữ liệu của từng trường và các ràng buộc (constraints).
// "products": <PERSON><PERSON><PERSON> là tên của collection trong database mà model này sẽ tương tác ( ví dụ ở đây là database in mongo atlas)
const Product = mongose.model("Product", productSchema, "products");

module.exports = Product;
