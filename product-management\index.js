const express = require("express");
const methodOverride = require("method-override");
const flash = require("express-flash");
const cookieParser = require("cookie-parser");
const session = require("express-session");

require("dotenv").config(); // .env

const systemConfix = require("./config/system");

const database = require("./config/database");

const router = require("./routers/client/index.route"); // cấu hình router
const routerAdmin = require("./routers/admin/index.route"); // cấu hình router admin

const app = express();
const port = process.env.PORT; // lấy port từ file .env

// Cấu hình middleware
app.use(express.urlencoded({ extended: true }));
app.use(methodOverride("_method"));

//Express Flash
app.use(cookieParser("keyboard cat"));
app.use(
  session({
    secret: "keyboard cat",
    resave: false,
    saveUninitialized: true,
    cookie: { maxAge: 60000 },
  })
);
app.use(flash());

app.use(express.static(`${__dirname}/public`));

database.connect();

router(app); // sử dụng router
routerAdmin(app);

app.set("views", `${__dirname}/view`);
app.set("view engine", "pug");

app.locals.prefixAdmin = systemConfix.prefixAdmin;

app.listen(port, () => {
  console.log(`Example app listening on port ${port}`);
});
