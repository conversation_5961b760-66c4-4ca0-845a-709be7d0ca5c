module.exports = (hehe) => {
  // lọc và tìm kiếm
  let filterStatus = [
    {
      name: "Tất Cả",
      status: "",
      class: "",
    },
    {
      name: "<PERSON><PERSON><PERSON> Động",
      status: "active",
      class: "",
    },
    {
      name: "<PERSON>ừng Hoạt Động",
      status: "inactive",
      class: "",
    },
  ];
  //hehe = req.query (product-controller)
  if (hehe.status) {
    const index = filterStatus.findIndex((item) => item.status == hehe.status);
    filterStatus[index].class = "active";
  } else {
    const index = filterStatus.findIndex((item) => item.status == "");
    filterStatus[index].class = "active";
  }
  return filterStatus;
};
